/**
 * AlertDashboardPage Component
 * Reusable page component for the alert dashboard
 */

'use client';

import React from 'react';
import { AlertDashboardLayout } from '@/components/features/alert-dashboard';

interface AlertDashboardPageProps {
    className?: string;
}

/**
 * AlertDashboardPage component
 * Provides the complete alert dashboard page with layout and content
 */
function AlertDashboardPage({ className }: AlertDashboardPageProps) {
    return (
        <AlertDashboardLayout className={className}>
            {/* Alert dashboard content area */}
            <div className="alert-dashboard-content-wrapper">
                <div className="content-header">
                    <h1 className="text-2xl font-bold text-white mb-4">Alert Dashboard</h1>
                    <p className="text-gray-300 mb-6">Monitor and manage system alerts in real-time</p>
                </div>

                {/* Main content grid */}
                <div className="content-grid">
                    {/* Floor plan area placeholder */}
                    <div className="floor-plan-area">
                        <div className="placeholder-card">
                            <h3 className="text-lg font-semibold text-white mb-2">2D Floor Plan</h3>
                            <p className="text-gray-400">
                                Interactive floor plan with device indicators will be displayed here
                            </p>
                            <div className="floor-plan-placeholder">
                                {/* Placeholder for floor plan visualization */}
                                <div className="grid grid-cols-8 gap-1 mt-4">
                                    {Array.from({ length: 32 }, (_, i) => (
                                        <div
                                            key={i}
                                            className={`w-8 h-8 rounded ${
                                                i % 7 === 0
                                                    ? 'bg-red-500'
                                                    : i % 5 === 0
                                                      ? 'bg-yellow-500'
                                                      : 'bg-gray-600'
                                            }`}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Alert summary area */}
                    <div className="alert-summary-area">
                        <div className="placeholder-card">
                            <h3 className="text-lg font-semibold text-white mb-2">Alert Summary</h3>
                            <div className="alert-stats">
                                <div className="stat-item">
                                    <span className="stat-number text-red-400">12</span>
                                    <span className="stat-label text-gray-400">Critical</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-number text-yellow-400">8</span>
                                    <span className="stat-label text-gray-400">Warning</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-number text-blue-400">24</span>
                                    <span className="stat-label text-gray-400">Info</span>
                                </div>
                            </div>

                            {/* Recent alerts list */}
                            <div className="recent-alerts mt-6">
                                <h4 className="text-md font-medium text-white mb-3">Recent Alerts</h4>
                                <div className="space-y-2">
                                    {[
                                        {
                                            type: 'critical',
                                            message: 'Fire alarm triggered in Zone A',
                                            time: '2 min ago',
                                        },
                                        { type: 'warning', message: 'Temperature sensor offline', time: '5 min ago' },
                                        { type: 'info', message: 'System maintenance scheduled', time: '10 min ago' },
                                    ].map((alert, index) => (
                                        <div key={index} className="alert-item">
                                            <div className={`alert-indicator ${alert.type}`}></div>
                                            <div className="alert-content">
                                                <p className="text-sm text-gray-300">{alert.message}</p>
                                                <span className="text-xs text-gray-500">{alert.time}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style jsx>{`
                .alert-dashboard-content-wrapper {
                    height: 100%;
                    padding: 1.5rem;
                    overflow-y: auto;
                }

                .content-grid {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: 1.5rem;
                    height: calc(100% - 120px);
                }

                .floor-plan-area,
                .alert-summary-area {
                    min-height: 300px;
                }

                .placeholder-card {
                    background-color: #333;
                    border: 1px solid #444;
                    border-radius: 8px;
                    padding: 1.5rem;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                }

                .alert-stats {
                    display: flex;
                    gap: 2rem;
                    margin-top: 1rem;
                }

                .stat-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 0.5rem;
                }

                .stat-number {
                    font-size: 2rem;
                    font-weight: bold;
                }

                .stat-label {
                    font-size: 0.875rem;
                }

                .alert-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 0.75rem;
                    padding: 0.5rem;
                    border-radius: 4px;
                    background-color: #2a2a2a;
                }

                .alert-indicator {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-top: 4px;
                    flex-shrink: 0;
                }

                .alert-indicator.critical {
                    background-color: #ef4444;
                }

                .alert-indicator.warning {
                    background-color: #f59e0b;
                }

                .alert-indicator.info {
                    background-color: #3b82f6;
                }

                .alert-content {
                    flex: 1;
                }

                @media (max-width: 768px) {
                    .content-grid {
                        grid-template-columns: 1fr;
                        gap: 1rem;
                    }

                    .alert-stats {
                        gap: 1rem;
                    }
                }
            `}</style>
        </AlertDashboardLayout>
    );
}

export default AlertDashboardPage;
