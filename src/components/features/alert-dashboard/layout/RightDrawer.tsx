'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard with full height and external toggle
 */
function RightDrawer({ isOpen = true, onToggle, isMobile = false, isTablet = false, className }: RightDrawerProps) {
    return (
        <>
            {/* زرار الفتح يظهر برة لما الـ drawer مقفول */}
            {!isOpen && (
                <button
                    onClick={onToggle}
                    className="fixed top-4 right-4 z-50 bg-gray-800 text-gray-200 p-2 rounded-full shadow-lg hover:bg-gray-600 transition-all duration-200"
                    aria-label="Open drawer"
                >
                    ⚙
                </button>
            )}

            <div
                className={`fixed top-0 right-0 h-full w-80 bg-gray-800 border-l border-gray-600 transform transition-transform duration-300 ease-in-out z-40 ${
                    isOpen ? 'translate-x-0' : 'translate-x-full'
                } ${className || ''}`}
            >
                <div className="h-full flex flex-col">
                    {/* Header with close toggle */}
                    <div className="flex items-center justify-between p-4 border-b border-gray-600">
                        <h3 className="text-white text-lg font-semibold">System Overview</h3>
                        <button
                            onClick={onToggle}
                            className="bg-transparent border-none text-gray-300 text-xl cursor-pointer p-1 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                            aria-label="Close drawer"
                        >
                            ✕
                        </button>
                    </div>

                    {/* محتوى الـ drawer */}
                    <div className="flex-1 p-4 text-gray-200">
                        Drawer content here...
                    </div>
                </div>
            </div>
        </>
    );
}

export default RightDrawer;
