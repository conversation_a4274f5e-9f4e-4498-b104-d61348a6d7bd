'use client';

import React, { useState } from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard
 */
function RightDrawer({
    isOpen = true,
    onToggle,
    isMobile = false,
    isTablet = false,
    className
}: RightDrawerProps) {


    return (
        <div className={`${styles['alert-dashboard-drawer']} ${isOpen ? styles.open : styles.closed} ${className || ''}`}>
            <div className={styles['drawer-placeholder']}>
                {/* Header */}
                <div className={styles['drawer-header']}>
                    <h3>System Overview</h3>
                    {(isMobile || isTablet) && (
                        <button
                            onClick={onToggle}
                            className="close-btn"
                            aria-label="Close drawer"
                        >
                            ✕
                        </button>
                    )}
                </div>

                
            </div>

            <style jsx>{`
                .close-btn {
                    background: none;
                    border: none;
                    color: #ccc;
                    font-size: 1.2rem;
                    cursor: pointer;
                    padding: 0.25rem;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .close-btn:hover {
                    background-color: #333;
                    color: #fff;
                }

                .tab-navigation {
                    display: flex;
                    border-bottom: 1px solid #333;
                    margin-bottom: 1rem;
                }

                .tab-btn {
                    flex: 1;
                    background: none;
                    border: none;
                    color: #ccc;
                    padding: 0.75rem 0.5rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 0.9rem;
                    border-bottom: 2px solid transparent;
                }

                .tab-btn:hover {
                    color: #fff;
                    background-color: #333;
                }

                .tab-btn.active {
                    color: #fff;
                    border-bottom-color: #666;
                }

                .content-section {
                    margin-bottom: 1.5rem;
                }

                .section-title {
                    color: #fff;
                    font-size: 1rem;
                    font-weight: 600;
                    margin: 0 0 0.75rem 0;
                }

                .system-list,
                .floor-list,
                .alert-list {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .system-card,
                .floor-card {
                    background-color: #333;
                    border: 1px solid #444;
                    border-radius: 6px;
                    padding: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    transition: all 0.2s ease;
                }

                .system-card:hover,
                .floor-card:hover {
                    background-color: #3a3a3a;
                    border-color: #555;
                }

                .system-info,
                .floor-info {
                    flex: 1;
                }

                .system-name,
                .floor-name {
                    color: #fff;
                    font-size: 0.9rem;
                    font-weight: 500;
                    margin-bottom: 0.25rem;
                }

                .system-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .status-indicator {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                }

                .status-text {
                    color: #ccc;
                    font-size: 0.8rem;
                    text-transform: capitalize;
                }

                .floor-stats {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .device-count {
                    color: #ccc;
                    font-size: 0.8rem;
                }

                .alert-count {
                    background-color: #ef4444;
                    color: #fff;
                    font-size: 0.8rem;
                    font-weight: bold;
                    padding: 0.25rem 0.5rem;
                    border-radius: 12px;
                    min-width: 24px;
                    text-align: center;
                }

                .alert-count-small {
                    color: #ef4444;
                    font-size: 0.8rem;
                    font-weight: 500;
                }

                .alert-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 0.75rem;
                    padding: 0.75rem;
                    background-color: #333;
                    border-radius: 6px;
                    border: 1px solid #444;
                }

                .alert-indicator {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-top: 4px;
                    flex-shrink: 0;
                }

                .alert-content {
                    flex: 1;
                }

                .alert-message {
                    color: #fff;
                    font-size: 0.9rem;
                    margin-bottom: 0.25rem;
                    line-height: 1.4;
                }

                .alert-time {
                    color: #999;
                    font-size: 0.8rem;
                }
            `}</style>
        </div>
    );
}

export default RightDrawer;
