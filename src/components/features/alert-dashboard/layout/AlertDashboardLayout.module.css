/* AlertDashboardLayout Styles */

/* Base layout */
.alert-dashboard-layout {
  display: grid;
  grid-template-areas: 
    "sidebar topbar topbar"
    "sidebar content drawer";
  grid-template-columns: 60px 1fr 300px;
  grid-template-rows: 60px 1fr;
  height: 100vh;
  overflow: hidden;
  background-color: #1a1a1a;
}

/* Grid areas */
.alert-dashboard-sidebar {
  grid-area: sidebar;
  background-color: #1a1a1a;
  border-right: 1px solid #333;
  transition: width 0.3s ease;
}

.alert-dashboard-topbar {
  grid-area: topbar;
  background-color: #0D131FF2;
  border-bottom: 1px solid #333;
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.alert-dashboard-content {
  grid-area: content;
  background-color: #2a2a2a;
  overflow: hidden;
  padding: 1rem;
}

.alert-dashboard-drawer {
  grid-area: drawer;
  background-color: #2a2a2a;
  border-left: 1px solid #333;
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.alert-dashboard-drawer.closed {
  transform: translateX(100%);
}

/* Sidebar states */
.alert-dashboard-layout.sidebar-expanded {
  grid-template-columns: 240px 1fr 300px;
}

.alert-dashboard-layout.sidebar-collapsed {
  grid-template-columns: 60px 1fr 300px;
}

/* Drawer states */
.alert-dashboard-layout.drawer-closed {
  grid-template-columns: 60px 1fr 0px;
}

.alert-dashboard-layout.drawer-closed.sidebar-expanded {
  grid-template-columns: 240px 1fr 0px;
}

/* Placeholder styles */
.sidebar-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem 0.5rem;
}

.sidebar-logo-area {
  height: 60px;
  border-bottom: 1px solid #333;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
}

.sidebar-nav-area {
  flex: 1;
  color: #ccc;
}

.topbar-placeholder {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topbar-left,
.topbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.topbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
}

.sidebar-toggle-btn,
.drawer-toggle-btn {
  background: none;
  border: 1px solid #333;
  color: #ccc;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar-toggle-btn:hover,
.drawer-toggle-btn:hover {
  background-color: #333;
  color: #fff;
}

.drawer-placeholder {
  height: 100%;
  padding: 1rem;
  color: #ccc;
}

.drawer-header {
  border-bottom: 1px solid #333;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.drawer-header h3 {
  margin: 0;
  color: #fff;
  font-size: 1.1rem;
}

.drawer-content {
  flex: 1;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .alert-dashboard-layout {
    grid-template-columns: 60px 1fr;
    grid-template-areas: 
      "sidebar topbar"
      "sidebar content";
  }
  
  .alert-dashboard-layout.sidebar-expanded {
    grid-template-columns: 240px 1fr;
  }
  
  .alert-dashboard-drawer {
    position: fixed;
    right: 0;
    top: 60px;
    height: calc(100vh - 60px);
    width: 300px;
    z-index: 1000;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3);
  }
}

@media (max-width: 768px) {
  .alert-dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "topbar"
      "content";
  }
  
  .alert-dashboard-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    width: 60px;
    z-index: 1001;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
  }
  
  .alert-dashboard-layout.sidebar-expanded .alert-dashboard-sidebar {
    width: 240px;
  }
  
  .alert-dashboard-drawer {
    width: 280px;
  }
}
