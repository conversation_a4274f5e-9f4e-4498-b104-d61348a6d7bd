'use client';

import React, { useState, useEffect } from 'react';
import { AlertDashboardLayoutProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * AlertDashboardLayout Component
 * Main layout container for the alert dashboard with responsive grid system
 */
function AlertDashboardLayout({
    children,
    className,
    leftSidebarCollapsed = false,
    rightDrawerOpen = true,
    onLeftSidebarToggle,
    onRightDrawerToggle,
}: AlertDashboardLayoutProps) {
    // Internal state for layout control
    const [sidebarCollapsed, setSidebarCollapsed] = useState(leftSidebarCollapsed);
    const [drawerOpen, setDrawerOpen] = useState(rightDrawerOpen);

    // Handle responsive breakpoints
    const [isMobile, setIsMobile] = useState(false);
    const [isTablet, setIsTablet] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            setIsMobile(width < 768);
            setIsTablet(width >= 768 && width < 1024);
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Update internal state when props change
    useEffect(() => {
        setSidebarCollapsed(leftSidebarCollapsed);
    }, [leftSidebarCollapsed]);

    useEffect(() => {
        setDrawerOpen(rightDrawerOpen);
    }, [rightDrawerOpen]);

    // Handle sidebar toggle
    const handleSidebarToggle = () => {
        const newCollapsed = !sidebarCollapsed;
        setSidebarCollapsed(newCollapsed);
        onLeftSidebarToggle?.();
    };

    // Handle drawer toggle
    const handleDrawerToggle = () => {
        const newOpen = !drawerOpen;
        setDrawerOpen(newOpen);
        onRightDrawerToggle?.();
    };

    // Generate CSS classes based on state
    const getLayoutClasses = () => {
        const baseClasses = styles['alert-dashboard-layout'];
        const responsiveClasses = isMobile ? styles.mobile : isTablet ? styles.tablet : styles.desktop;
        const sidebarClasses = sidebarCollapsed ? styles['sidebar-collapsed'] : styles['sidebar-expanded'];
        const drawerClasses = drawerOpen ? styles['drawer-open'] : styles['drawer-closed'];

        return `${baseClasses} ${responsiveClasses || ''} ${sidebarClasses || ''} ${drawerClasses || ''} ${className || ''}`;
    };

    return (
        <div className={getLayoutClasses()}>
            {/* Left Sidebar Slot */}
            <div className={styles['alert-dashboard-sidebar']}>
                {/* Empty for now - will be filled by LeftSidebar component */}
                <div className={styles['sidebar-placeholder']}>
                    <div className={styles['sidebar-logo-area']}>{/* Logo placeholder */}</div>
                    <div className={styles['sidebar-nav-area']}>{/* Navigation placeholder */}</div>
                </div>
            </div>

            {/* Top Bar Slot */}
            <div className={styles['alert-dashboard-topbar']}>
                {/* Empty for now - will be filled by TopBar component */}
                <div className={styles['topbar-placeholder']}>
                    <div className={styles['topbar-left']}>
                        <button
                            onClick={handleSidebarToggle}
                            className={styles['sidebar-toggle-btn']}
                            aria-label="Toggle sidebar">
                            ☰
                        </button>
                    </div>
                    <div className={styles['topbar-center']}>{/* Filters and search placeholder */}</div>
                    <div className={styles['topbar-right']}>
                        <button
                            onClick={handleDrawerToggle}
                            className={styles['drawer-toggle-btn']}
                            aria-label="Toggle drawer">
                            ⚙
                        </button>
                    </div>
                </div>
            </div>

            {/* Main Content Area */}
            <div className={styles['alert-dashboard-content']}>{children}</div>

            {/* Right Drawer Slot */}
            <div className={`${styles['alert-dashboard-drawer']} ${drawerOpen ? styles.open : styles.closed}`}>
                {/* Empty for now - will be filled by RightDrawer component */}
                <div className={styles['drawer-placeholder']}>
                    <div className={styles['drawer-header']}>
                        <h3>System Overview</h3>
                    </div>
                    <div className={styles['drawer-content']}>{/* System cards placeholder */}</div>
                </div>
            </div>
        </div>
    );
}

export default AlertDashboardLayout;
