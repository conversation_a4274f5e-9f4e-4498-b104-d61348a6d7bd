'use client';

import React, { useState } from 'react';
import { TopBarProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * TopBar Component
 * Header with filters and controls for the alert dashboard
 */
function TopBar({
    onSidebarToggle,
    onDrawerToggle,
    isMobile = false,
    isTablet = false,
    className
}: TopBarProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedFilter, setSelectedFilter] = useState('all');

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedFilter(e.target.value);
    };

    return (
        <div className={`${styles['alert-dashboard-topbar']} ${className || ''}`}>
            <div className={styles['topbar-placeholder']}>
                {/* Left Section - Controls */}
                <div className={styles['topbar-left']}>
                    <button
                        onClick={onSidebarToggle}
                        className={styles['sidebar-toggle-btn']}
                        aria-label="Toggle sidebar"
                    >
                        ☰
                    </button>
                    
                    {!isMobile && (
                        <div className="breadcrumb">
                            <span className="breadcrumb-item">Alert Dashboard</span>
                            <span className="breadcrumb-separator">/</span>
                            <span className="breadcrumb-item current">Overview</span>
                        </div>
                    )}
                </div>

                

                {/* Right Section - Actions */}
                <div className={styles['topbar-right']}>
                    
                    <button
                        onClick={onDrawerToggle}
                        className={styles['drawer-toggle-btn']}
                        aria-label="Toggle drawer"
                    >
                        ⚙
                    </button>
                </div>
            </div>

            <style jsx>{`
                .breadcrumb {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-left: 1rem;
                    color: #ccc;
                    font-size: 0.9rem;
                }

                .breadcrumb-item {
                    color: #999;
                }

                .breadcrumb-item.current {
                    color: #fff;
                    font-weight: 500;
                }

                .breadcrumb-separator {
                    color: #666;
                }

                .search-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                }

                .search-input {
                    background-color: #333;
                    border: 1px solid #444;
                    border-radius: 6px;
                    padding: 0.5rem 2.5rem 0.5rem 1rem;
                    color: #fff;
                    font-size: 0.9rem;
                    width: 300px;
                    transition: all 0.2s ease;
                }

                .search-input:focus {
                    outline: none;
                    border-color: #666;
                    background-color: #2a2a2a;
                }

                .search-input::placeholder {
                    color: #999;
                }

                .search-icon {
                    position: absolute;
                    right: 0.75rem;
                    color: #999;
                    pointer-events: none;
                }

                .filter-container {
                    margin-left: 1rem;
                }

                .filter-select {
                    background-color: #333;
                    border: 1px solid #444;
                    border-radius: 6px;
                    padding: 0.5rem 0.75rem;
                    color: #fff;
                    font-size: 0.9rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .filter-select:focus {
                    outline: none;
                    border-color: #666;
                    background-color: #2a2a2a;
                }

                .filter-select option {
                    background-color: #333;
                    color: #fff;
                }

                .action-buttons {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-right: 1rem;
                }

                .action-btn {
                    background: none;
                    border: 1px solid #444;
                    color: #ccc;
                    padding: 0.5rem;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 1rem;
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }

                .action-btn:hover {
                    background-color: #333;
                    color: #fff;
                    border-color: #666;
                }

                .notification-btn {
                    position: relative;
                }

                .notification-badge {
                    position: absolute;
                    top: -4px;
                    right: -4px;
                    background-color: #ef4444;
                    color: #fff;
                    font-size: 0.7rem;
                    font-weight: bold;
                    padding: 2px 6px;
                    border-radius: 10px;
                    min-width: 18px;
                    height: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                @media (max-width: 768px) {
                    .search-input {
                        width: 200px;
                    }
                }

                @media (max-width: 480px) {
                    .search-input {
                        width: 150px;
                        font-size: 0.8rem;
                    }
                    
                    .breadcrumb {
                        display: none;
                    }
                }
            `}</style>
        </div>
    );
}

export default TopBar;
