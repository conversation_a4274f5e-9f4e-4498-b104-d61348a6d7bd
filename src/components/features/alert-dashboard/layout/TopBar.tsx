'use client';

import React, { useState } from 'react';
import { TopBarProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * TopBar Component
 * Header with filters and controls for the alert dashboard
 */
function TopBar({ onSidebarToggle, isMobile = false, isTablet = false, className }: TopBarProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedFilter, setSelectedFilter] = useState('all');

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedFilter(e.target.value);
    };

    return (
        <div className={`${styles['alert-dashboard-topbar']} ${className || ''}`}>
            <div className="w-full flex items-center justify-between px-4">
                {/* Left Section - Controls */}
                <div className="flex items-center gap-2">
                    <button
                        onClick={onSidebarToggle}
                        className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Toggle sidebar">
                        ☰
                    </button>

                    {!isMobile && (
                        <div className="flex items-center gap-2 ml-4 text-gray-300 text-sm">
                            <span className="text-gray-400">Alert Dashboard</span>
                            <span className="text-gray-600">/</span>
                            <span className="text-white font-medium">Overview</span>
                        </div>
                    )}
                </div>

                {/* Center Section - Search and Filters */}
                <div className="flex-1 flex items-center justify-center gap-4">
                    <div className="relative flex items-center">
                        <input
                            type="text"
                            placeholder="Search alerts..."
                            value={searchQuery}
                            onChange={handleSearchChange}
                            className="bg-gray-700 border border-gray-600 rounded-md px-4 pr-10 py-2 text-white text-sm w-80 transition-all duration-200 focus:outline-none focus:border-gray-500 focus:bg-gray-600 placeholder-gray-400"
                        />
                        <span className="absolute right-3 text-gray-400 pointer-events-none">🔍</span>
                    </div>

                    {!isMobile && (
                        <select
                            value={selectedFilter}
                            onChange={handleFilterChange}
                            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white text-sm cursor-pointer transition-all duration-200 focus:outline-none focus:border-gray-500 focus:bg-gray-600">
                            <option value="all">All Alerts</option>
                            <option value="critical">Critical</option>
                            <option value="warning">Warning</option>
                            <option value="info">Info</option>
                        </select>
                    )}
                </div>

                {/* Right Section - Actions */}
                <div className="flex items-center gap-2">
                    {!isMobile && (
                        <div className="flex items-center gap-2 mr-4">
                            <button
                                className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200 w-9 h-9 flex items-center justify-center"
                                title="Refresh">
                                🔄
                            </button>
                            <button
                                className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200 w-9 h-9 flex items-center justify-center"
                                title="Export">
                                📤
                            </button>
                            <button
                                className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200 w-9 h-9 flex items-center justify-center relative"
                                title="Notifications">
                                🔔
                                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                                    3
                                </span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default TopBar;
