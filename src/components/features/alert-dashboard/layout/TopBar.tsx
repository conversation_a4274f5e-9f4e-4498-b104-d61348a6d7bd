'use client';

import React, { useState } from 'react';
import { TopBarProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * TopBar Component
 * Header with filters and controls for the alert dashboard
 */
function TopBar({ onSidebarToggle, isMobile = false, isTablet = false, className }: TopBarProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedFilter, setSelectedFilter] = useState('all');

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedFilter(e.target.value);
    };

    return (
        <div className={`${styles['alert-dashboard-topbar']} ${className || ''}`}>
            <div className="w-full flex items-center justify-between px-4">
                {/* Left Section - Controls */}
                <div className="flex items-center gap-2">
                    <button
                        onClick={onSidebarToggle}
                        className="bg-transparent border border-gray-600 text-gray-300 p-2 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Toggle sidebar">
                        ☰
                    </button>

                    
                </div>
            </div>
        </div>
    );
}

export default TopBar;
