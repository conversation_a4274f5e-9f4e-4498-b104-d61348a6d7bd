'use client';

import React from 'react';
import { ContentBodyProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * ContentBody Component
 * Main content area wrapper for the alert dashboard
 */
function ContentBody({ children, className }: ContentBodyProps) {
    return <div className={`${styles['alert-dashboard-content']} ${className || ''}`}>{children}</div>;
}

export default ContentBody;
