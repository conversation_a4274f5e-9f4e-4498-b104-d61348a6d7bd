'use client';

import React from 'react';
import { LeftSidebarProps } from '../alert-dashboard.types';
import styles from './AlertDashboardLayout.module.css';

/**
 * LeftSidebar Component
 * Navigation sidebar for the alert dashboard
 */
function LeftSidebar({
    collapsed = false,
    onToggle,
    isMobile = false,
    isTablet = false,
    className
}: LeftSidebarProps) {
    return (
        <div className={`${styles['alert-dashboard-sidebar']} ${className || ''}`}>
            <div className={styles['sidebar-placeholder']}>
                {/* Logo Area */}
                <div className={styles['sidebar-logo-area']}>
                    <div className="logo-container">
                        {!collapsed && (
                            <span className="logo-text">
                                Alert Dashboard
                            </span>
                        )}
                        {collapsed && (
                            <span className="logo-icon">
                                AD
                            </span>
                        )}
                    </div>
                </div>

                {/* Navigation Area */}
                <div className={styles['sidebar-nav-area']}>
                    <nav className="nav-menu">
                        <ul className="nav-list">
                            <li className="nav-item active">
                                <a href="#" className="nav-link">
                                    <span className="nav-icon">🏠</span>
                                    {!collapsed && <span className="nav-text">Dashboard</span>}
                                </a>
                            </li>
                            <li className="nav-item">
                                <a href="#" className="nav-link">
                                    <span className="nav-icon">🚨</span>
                                    {!collapsed && <span className="nav-text">Alerts</span>}
                                </a>
                            </li>
                            <li className="nav-item">
                                <a href="#" className="nav-link">
                                    <span className="nav-icon">📊</span>
                                    {!collapsed && <span className="nav-text">Analytics</span>}
                                </a>
                            </li>
                            <li className="nav-item">
                                <a href="#" className="nav-link">
                                    <span className="nav-icon">⚙️</span>
                                    {!collapsed && <span className="nav-text">Settings</span>}
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>

                {/* Collapse Toggle (for desktop) */}
                {!isMobile && !isTablet && (
                    <div className="sidebar-footer">
                        <button
                            onClick={onToggle}
                            className="collapse-btn"
                            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
                        >
                            {collapsed ? '→' : '←'}
                        </button>
                    </div>
                )}
            </div>

            <style jsx>{`
                .logo-container {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: #fff;
                    font-weight: bold;
                }

                .logo-text {
                    font-size: 0.9rem;
                    text-align: center;
                }

                .logo-icon {
                    font-size: 1.2rem;
                    font-weight: bold;
                }

                .nav-menu {
                    height: 100%;
                }

                .nav-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .nav-item {
                    margin: 0;
                }

                .nav-link {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.75rem;
                    color: #ccc;
                    text-decoration: none;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                    min-height: 44px;
                }

                .nav-link:hover {
                    background-color: #333;
                    color: #fff;
                }

                .nav-item.active .nav-link {
                    background-color: #444;
                    color: #fff;
                }

                .nav-icon {
                    font-size: 1.2rem;
                    width: 20px;
                    text-align: center;
                    flex-shrink: 0;
                }

                .nav-text {
                    font-size: 0.9rem;
                    white-space: nowrap;
                }

                .sidebar-footer {
                    margin-top: auto;
                    padding: 1rem 0.5rem;
                    border-top: 1px solid #333;
                }

                .collapse-btn {
                    width: 100%;
                    padding: 0.5rem;
                    background: none;
                    border: 1px solid #333;
                    color: #ccc;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 1rem;
                }

                .collapse-btn:hover {
                    background-color: #333;
                    color: #fff;
                }

                @media (max-width: 768px) {
                    .nav-link {
                        justify-content: center;
                    }
                    
                    .nav-text {
                        display: none;
                    }
                }
            `}</style>
        </div>
    );
}

export default LeftSidebar;
