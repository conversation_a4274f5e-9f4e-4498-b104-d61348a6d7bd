# Task 001: Dashboard Layout Container Component

## Routing Integration

**Primary Page**: `AlertDashboard`
**Route**: `/dashboard/alerts`
**Page Location**: `src/app/alert-dashboard/page.tsx` (Next.js App Router)
**Integration**: This layout component will be used as the wrapper for the AlertDashboard page

## Task Overview

**Component Name**: `DashboardLayout`
**Priority**: High
**Developer Assignment**: Developer 1 (Layout Specialist)
**Dependencies**: None (Base component)
**Location**: `src/components/features/alert-dashboard/layout/AlertDashboardLayout.tsx`

## Component Description

Create the main dashboard layout container that serves as the wrapper for all dashboard components. This component establishes the grid structure and manages the overall layout behavior including responsive design and component positioning.

## Technical Specifications

### Component Structure

```typescript
interface DashboardLayoutProps {
  children?: React.ReactNode;
  className?: string;
  leftSidebarCollapsed?: boolean;
  rightDrawerOpen?: boolean;
  onLeftSidebarToggle?: () => void;
  onRightDrawerToggle?: () => void;
}
```

### Layout Grid System

Based on the design specifications:

- **Left Sidebar**: ~80px width (collapsed state)
- **Main Content**: ~70% of viewport width (flexible)
- **Right Sidebar**: ~300px width (when open)
- **Top Bar**: ~80px height (fixed)

### CSS Grid Layout Structure

```css
.dashboard-layout {
  display: grid;
  grid-template-areas: 
    "sidebar topbar topbar"
    "sidebar content drawer";
  grid-template-columns: 60px 1fr 300px;
  grid-template-rows: 60px 1fr;
  height: 100vh;
  overflow: hidden;
}
```

## Implementation Requirements

### 1. Base Layout Structure

```tsx
function DashboardLayout({
  children,
  className,
  leftSidebarCollapsed = false,
  rightDrawerOpen = true,
  onLeftSidebarToggle,
  onRightDrawerToggle
}: DashboardLayoutProps) {
  return (
    <div className={`dashboard-layout ${className || ''}`}>
      {/* Left Sidebar Slot */}
      <div className="dashboard-sidebar">
        {/* Empty for now - will be filled by LeftSidebar component */}
      </div>
  
      {/* Top Bar Slot */}
      <div className="dashboard-topbar">
        {/* Empty for now - will be filled by TopBar component */}
      </div>
  
      {/* Main Content Area */}
      <div className="dashboard-content">
        {children}
      </div>
  
      {/* Right Drawer Slot */}
      <div className={`dashboard-drawer ${rightDrawerOpen ? 'open' : 'closed'}`}>
        {/* Empty for now - will be filled by RightDrawer component */}
      </div>
    </div>
  );
}
```

### 2. Responsive Behavior

#### Desktop (>1024px)

- Full layout with all panels visible
- Right drawer open by default
- Left sidebar always visible

#### Tablet (768px - 1024px)

- Right drawer becomes overlay
- Left sidebar remains fixed
- Content area expands

#### Mobile (<768px)

- Both sidebars become overlays
- Full-width content area
- Touch-friendly toggle buttons

### 3. State Management

```typescript
// Internal state for layout control
const [sidebarCollapsed, setSidebarCollapsed] = useState(leftSidebarCollapsed);
const [drawerOpen, setDrawerOpen] = useState(rightDrawerOpen);

// Handle responsive breakpoints
const [isMobile, setIsMobile] = useState(false);
const [isTablet, setIsTablet] = useState(false);

useEffect(() => {
  const handleResize = () => {
    const width = window.innerWidth;
    setIsMobile(width < 768);
    setIsTablet(width >= 768 && width < 1024);
  };
  
  handleResize();
  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, []);
```

### 4. CSS Classes Structure

```css
/* Base layout */
.dashboard-layout {
  display: grid;
  height: 100vh;
  overflow: hidden;
  background-color: #1a1a1a;
}

/* Grid areas */
.dashboard-sidebar {
  grid-area: sidebar;
  background-color: #1a1a1a;
  border-right: 1px solid #333;
}

.dashboard-topbar {
  grid-area: topbar;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
}

.dashboard-content {
  grid-area: content;
  background-color: #2a2a2a;
  overflow: hidden;
}

.dashboard-drawer {
  grid-area: drawer;
  background-color: #2a2a2a;
  border-left: 1px solid #333;
  transition: transform 0.3s ease;
}

.dashboard-drawer.closed {
  transform: translateX(100%);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .dashboard-layout {
    grid-template-columns: 60px 1fr;
    grid-template-areas: 
      "sidebar topbar"
      "sidebar content";
  }
  
  .dashboard-drawer {
    position: fixed;
    right: 0;
    top: 60px;
    height: calc(100vh - 60px);
    width: 300px;
    z-index: 1000;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "topbar"
      "content";
  }
  
  .dashboard-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    width: 60px;
    z-index: 1001;
  }
}
```

## Component Integration Points

### 1. Child Component Slots

- **LeftSidebar**: Will be rendered in `.dashboard-sidebar`
- **TopBar**: Will be rendered in `.dashboard-topbar`
- **ContentBody**: Will be passed as `children` to `.dashboard-content`
- **RightDrawer**: Will be rendered in `.dashboard-drawer`

### 2. Props Interface for Child Components

```typescript
// Props that will be passed to child components
interface ChildComponentProps {
  collapsed?: boolean;  // For sidebar
  isOpen?: boolean;     // For drawer
  onToggle?: () => void; // For interactive components
  isMobile?: boolean;   // For responsive behavior
  isTablet?: boolean;   // For responsive behavior
}
```

## Testing Requirements

### 1. Unit Tests

- Component renders without errors
- Props are handled correctly
- State updates work as expected
- Responsive breakpoints trigger correctly

### 2. Integration Tests

- Layout maintains structure with child components
- Grid areas are properly assigned
- Responsive behavior works across breakpoints

### 3. Visual Tests

- Layout matches design specifications
- Transitions are smooth
- Dark theme colors are applied correctly

## Acceptance Criteria

- [ ] Component renders empty layout structure
- [ ] Grid system matches design specifications
- [ ] Responsive behavior works on all breakpoints
- [ ] Dark theme styling is applied
- [ ] State management for sidebar/drawer toggle works
- [ ] Component is properly typed with TypeScript
- [ ] CSS classes follow naming conventions
- [ ] Component is exported from index file
- [ ] Basic unit tests are written
- [ ] Component integrates with existing Layout component

## File Structure Output

```
src/components/features/dashboard/
├── layout/
│   ├── DashboardLayout.tsx       # Main component file
│   ├── DashboardLayout.module.css # Component-specific styles
│   └── index.ts                  # Export file
├── types.ts                      # Dashboard types
└── index.ts                      # Main export barrel
```

## Usage Example

```tsx
// Next.js App Router page (src/app/dashboard/alerts/page.tsx)
import { DashboardLayout } from '@/components/features/dashboard';

export default function AlertsPage() {
  return (
    <DashboardLayout>
      {/* Alert dashboard content will go here */}
      <div>Alert Dashboard content area</div>
    </DashboardLayout>
  );
}
```

### Page Structure

```
src/app/dashboard/alerts/
├── page.tsx              # Main alerts page
├── loading.tsx           # Loading UI (optional)
└── error.tsx             # Error UI (optional)
```

### Additional Next.js Integration

```tsx
// Optional: Create a reusable AlertDashboard component (src/components/pages/AlertDashboard.tsx)
import { DashboardLayout } from '@/components/features/dashboard';

export function AlertDashboard() {
  return (
    <DashboardLayout>
      {/* Alert dashboard content */}
      <div>Alert Dashboard content area</div>
    </DashboardLayout>
  );
}

// Then use in page.tsx:
import { AlertDashboard } from '@/components/pages';

export default function AlertsPage() {
  return <AlertDashboard />;
}
```

## Notes for Developer

1. **Start Simple**: Begin with basic grid layout, add complexity gradually
2. **Mobile First**: Implement responsive design from mobile up
3. **Dark Theme**: All colors should follow the dark theme palette
4. **Performance**: Use CSS Grid for optimal performance
5. **Accessibility**: Ensure proper ARIA labels and keyboard navigation
6. **Future Proof**: Structure should accommodate additional layout features

## Dependencies

- React 18+
- TypeScript
- CSS Modules or Tailwind CSS (based on project setup)
- No external UI libraries required for this component

---

**Estimated Development Time**: 1-2 days
**Review Required**: Yes, before proceeding to child components
**Documentation**: Update component README after completion
